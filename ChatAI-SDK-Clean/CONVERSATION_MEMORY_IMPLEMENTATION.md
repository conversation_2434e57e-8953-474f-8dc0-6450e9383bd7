# Conversation Memory Implementation

## Overview

This implementation adds session-based conversation memory to the RAG system, allowing the AI to remember the last 3 prompts and responses for context-aware conversations without storing data in PostgreSQL.

## Key Features

- ✅ **Session-based memory**: Each session maintains its own conversation history
- ✅ **Limited to 3 conversations**: Automatically removes older conversations to prevent memory bloat
- ✅ **No database storage**: Uses in-memory cache with TTL expiration (2 hours)
- ✅ **Automatic cleanup**: Expired conversations are automatically removed
- ✅ **Session isolation**: Different sessions have separate conversation histories

## Implementation Details

### 1. Cache Service Changes (`src/services/cacheService.js`)

#### Modified Methods:
- `addConversationEntry()`: Changed limit from 10 to 3 conversations
- `getConversationHistory()`: Returns raw conversation history
- `getFormattedConversationHistory()`: **NEW** - Formats history for OpenRouter LLM

#### New Method:
```javascript
getFormattedConversationHistory(sessionId) {
  // Returns array of {role: 'user'|'assistant', content: string}
  // Converts internal format to OpenRouter chat format
}
```

### 2. Route Handler Changes (`src/routes/index.js`)

#### Streaming Response:
```javascript
// Get conversation history before calling OpenRouter
const conversationHistory = cacheService.getFormattedConversationHistory(sessionId);

// Pass history to OpenRouter
const streamGenerator = openRouterService.generateStreamingResponse(query, context, conversationHistory);
```

#### Non-streaming Response:
```javascript
// Get conversation history before calling OpenRouter
const conversationHistory = cacheService.getFormattedConversationHistory(currentSessionId);

// Pass history to OpenRouter
const response = await openRouterService.generateResponse(query, finalContext, conversationHistory);
```

### 3. OpenRouter Service Changes (`src/services/openRouterService.js`)

#### Enhanced Message Building:
- Limited history to last 6 messages (3 conversations)
- Added logging for conversation history usage
- Maintains existing chat history parameter support

## Data Flow

```
User Query + sessionId
        ↓
1. Get conversation history from cache
        ↓
2. Retrieve context from vector search
        ↓
3. Combine: System Prompt + Conversation History + Current Query
        ↓
4. Send to OpenRouter LLM
        ↓
5. Store response in conversation cache
        ↓
6. Return response to user
```

## Conversation Format

### Internal Storage Format:
```javascript
{
  query: "User's question",
  response: "AI's response", 
  timestamp: 1234567890,
  metadata: { /* additional data */ }
}
```

### OpenRouter Format:
```javascript
[
  { role: "user", content: "User's question" },
  { role: "assistant", content: "AI's response" },
  { role: "user", content: "Follow-up question" },
  { role: "assistant", content: "Follow-up response" }
]
```

## Configuration

### Cache TTL Settings:
- **Conversation TTL**: 2 hours (7200000 ms)
- **Session TTL**: 15 minutes (900000 ms)
- **Cleanup Interval**: 5 minutes

### Memory Limits:
- **Max conversations per session**: 3
- **Max messages sent to LLM**: 6 (3 conversations × 2 messages each)
- **Max sessions in cache**: Configurable via `config.cache.maxSessions`

## Testing

### Unit Tests:
```bash
node test-conversation-memory.js
```

### Integration Tests:
```bash
# Start the server first
npm start

# Then run integration tests
node test-conversation-integration.js
```

## Usage Examples

### Basic Conversation:
```bash
# First query - creates new session
curl "http://localhost:3002/api/v1/?apikey=YOUR_KEY&query=What is ChatAI?"

# Follow-up query - uses conversation history
curl "http://localhost:3002/api/v1/?apikey=YOUR_KEY&query=How does it work?&sessionId=SESSION_ID"
```

### Streaming Conversation:
```bash
# Streaming with conversation memory
curl "http://localhost:3002/api/v1/?apikey=YOUR_KEY&query=Tell me more&sessionId=SESSION_ID&stream=true"
```

## Benefits

1. **Context Awareness**: AI remembers previous questions and can provide contextual responses
2. **No Database Overhead**: Uses efficient in-memory storage
3. **Automatic Cleanup**: Prevents memory leaks with TTL expiration
4. **Session Isolation**: Multiple users/sessions don't interfere with each other
5. **Configurable Limits**: Easy to adjust conversation history length

## Limitations

1. **Memory Only**: Conversations are lost on server restart
2. **Limited History**: Only last 3 conversations are remembered
3. **Session-based**: Requires sessionId for conversation continuity
4. **TTL Expiration**: Conversations expire after 2 hours of inactivity

## Future Enhancements

- Add conversation persistence option
- Implement conversation summarization for longer contexts
- Add user-specific conversation limits
- Support for conversation branching/forking
