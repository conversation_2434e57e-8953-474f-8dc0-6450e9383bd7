const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');
const config = require('../config');

class CacheService {
  constructor() {
    this.sessionCache = new Map();
    this.appCache = new Map();

    // 🆕 New caches for optimization
    this.apiKeyCache = new Map();      // API key validation cache
    this.contextCache = new Map();     // LlamaIndex context cache (query-specific)
    this.documentCache = new Map();    // Document content cache (by indexId)
    this.semanticCache = new Map();    // Semantic query category cache
    this.conversationCache = new Map(); // Conversation history cache

    this.ttl = config.cache.ttlMinutes * 60 * 1000; // Convert to milliseconds
    this.maxSessions = config.cache.maxSessions;
    this.cleanupInterval = config.cache.cleanupIntervalMinutes * 60 * 1000;

    // Cache TTLs for different types
    this.apiKeyTTL = 10 * 60 * 1000;    // 10 minutes for API key validation
    this.contextTTL = 30 * 60 * 1000;   // 30 minutes for query-specific context
    this.documentTTL = 60 * 60 * 1000;  // 1 hour for document content
    this.semanticTTL = 45 * 60 * 1000;  // 45 minutes for semantic categories
    this.conversationTTL = 2 * 60 * 60 * 1000; // 2 hours for conversation

    // Start cleanup interval
    this.startCleanupInterval();

    console.log('✅ CacheService initialized with TTL:', config.cache.ttlMinutes, 'minutes');
    console.log('🆕 Enhanced caching: API Key (10min), Context (30min), Documents (1hr), Semantic (45min), Conversation (2hr)');
  }

  /**
   * Generate a new session ID
   * @returns {string} Session ID
   */
  generateSessionId() {
    return uuidv4();
  }

  /**
   * Generate secure hash for API key
   * @param {string} apiKey - API key to hash
   * @returns {string} Hashed API key
   */
  hashApiKey(apiKey) {
    return crypto.createHash('sha256').update(apiKey).digest('hex');
  }

  /**
   * Cache API key validation result
   * @param {string} apiKey - API key
   * @param {Object} validationData - Validation result from User Service
   */
  cacheApiKeyValidation(apiKey, validationData) {
    const hashedKey = this.hashApiKey(apiKey);
    const cacheData = {
      data: validationData,
      timestamp: Date.now(),
      expiresAt: Date.now() + this.apiKeyTTL
    };

    this.apiKeyCache.set(hashedKey, cacheData);
    console.log(`🔑 Cached API key validation for: ${hashedKey.substring(0, 8)}...`);
  }

  /**
   * Get cached API key validation
   * @param {string} apiKey - API key
   * @returns {Object|null} Cached validation data or null if not found/expired
   */
  getCachedApiKeyValidation(apiKey) {
    const hashedKey = this.hashApiKey(apiKey);

    if (!this.apiKeyCache.has(hashedKey)) {
      return null;
    }

    const cached = this.apiKeyCache.get(hashedKey);

    if (Date.now() > cached.expiresAt) {
      this.apiKeyCache.delete(hashedKey);
      console.log(`⏰ API key validation expired: ${hashedKey.substring(0, 8)}...`);
      return null;
    }

    console.log(`✅ Using cached API key validation: ${hashedKey.substring(0, 8)}...`);
    return cached.data;
  }

  /**
   * Categorize query into semantic categories for better cache hits
   * @param {string} query - User query
   * @returns {string} Semantic category
   */
  categorizeQuery(query) {
    const lowerQuery = query.toLowerCase().trim();

    // Invoice/Amount related queries
    if (lowerQuery.match(/\b(invoice|amount|total|cost|price|bill|charge|fee)\b/)) {
      return 'invoice_amount';
    }

    // Date related queries
    if (lowerQuery.match(/\b(date|due|deadline|when|time|schedule|expiry|expire)\b/)) {
      return 'date_info';
    }

    // Payment related queries
    if (lowerQuery.match(/\b(payment|pay|transaction|transfer|method|card|bank)\b/)) {
      return 'payment_info';
    }

    // Contact/Address related queries
    if (lowerQuery.match(/\b(address|contact|phone|email|location|company|vendor|supplier)\b/)) {
      return 'contact_info';
    }

    // Item/Product related queries
    if (lowerQuery.match(/\b(item|product|service|description|quantity|unit|line)\b/)) {
      return 'item_details';
    }

    // Status related queries
    if (lowerQuery.match(/\b(status|paid|unpaid|pending|complete|approved|rejected)\b/)) {
      return 'status_info';
    }

    // For unique/specific queries, use hash
    return crypto.createHash('md5').update(lowerQuery).digest('hex').substring(0, 8);
  }

  /**
   * Generate context cache key with semantic categorization
   * @param {string} appId - Application ID
   * @param {string} query - User query
   * @returns {string} Cache key
   */
  generateContextKey(appId, query) {
    const semanticCategory = this.categorizeQuery(query);
    return `${appId}:${semanticCategory}`;
  }

  /**
   * Generate document cache key
   * @param {string} indexId - Document index ID
   * @returns {string} Cache key
   */
  generateDocumentKey(indexId) {
    return `doc:${indexId}`;
  }

  /**
   * Cache document content by indexId
   * @param {string} indexId - Document index ID
   * @param {Object} documentData - Document content and metadata
   */
  cacheDocument(indexId, documentData) {
    const cacheKey = this.generateDocumentKey(indexId);
    const cacheData = {
      ...documentData,
      timestamp: Date.now(),
      expiresAt: Date.now() + this.documentTTL
    };

    this.documentCache.set(cacheKey, cacheData);
    console.log(`📄 Cached document content for indexId: ${indexId}`);
  }

  /**
   * Get cached document content
   * @param {string} indexId - Document index ID
   * @returns {Object|null} Cached document data or null if not found/expired
   */
  getCachedDocument(indexId) {
    const cacheKey = this.generateDocumentKey(indexId);

    if (!this.documentCache.has(cacheKey)) {
      return null;
    }

    const cached = this.documentCache.get(cacheKey);

    if (Date.now() > cached.expiresAt) {
      this.documentCache.delete(cacheKey);
      console.log(`⏰ Document cache expired for indexId: ${indexId}`);
      return null;
    }

    console.log(`✅ Using cached document for indexId: ${indexId}`);
    return cached;
  }

  /**
   * Cache LlamaIndex context result with semantic categorization
   * @param {string} appId - Application ID
   * @param {string} query - User query
   * @param {string} context - Retrieved context
   * @param {Array} documents - Documents used
   */
  cacheContext(appId, query, context, documents = []) {
    const cacheKey = this.generateContextKey(appId, query);
    const semanticCategory = this.categorizeQuery(query);

    const cacheData = {
      context,
      documents,
      query,
      semanticCategory,
      timestamp: Date.now(),
      expiresAt: Date.now() + this.contextTTL
    };

    this.contextCache.set(cacheKey, cacheData);
    console.log(`🔍 Cached context for appId: ${appId}, category: "${semanticCategory}", query: "${query.substring(0, 30)}..."`);
  }

  /**
   * Get cached context with semantic matching
   * @param {string} appId - Application ID
   * @param {string} query - User query
   * @returns {Object|null} Cached context data or null if not found/expired
   */
  getCachedContext(appId, query) {
    const cacheKey = this.generateContextKey(appId, query);
    const semanticCategory = this.categorizeQuery(query);

    if (!this.contextCache.has(cacheKey)) {
      console.log(`🔍 Cache miss for appId: ${appId}, category: "${semanticCategory}"`);
      return null;
    }

    const cached = this.contextCache.get(cacheKey);

    if (Date.now() > cached.expiresAt) {
      this.contextCache.delete(cacheKey);
      console.log(`⏰ Context cache expired for appId: ${appId}, category: "${semanticCategory}"`);
      return null;
    }

    console.log(`✅ Using cached context for appId: ${appId}, category: "${semanticCategory}", original query: "${cached.query.substring(0, 30)}..."`);
    console.log(`🎯 Current query: "${query.substring(0, 30)}..." matched with cached category`);
    return cached;
  }

  /**
   * Add conversation entry to session
   * @param {string} sessionId - Session ID
   * @param {string} query - User query
   * @param {string} response - AI response
   * @param {Object} metadata - Additional metadata
   */
  addConversationEntry(sessionId, query, response, metadata = {}) {
    if (!this.conversationCache.has(sessionId)) {
      this.conversationCache.set(sessionId, {
        history: [],
        timestamp: Date.now(),
        expiresAt: Date.now() + this.conversationTTL
      });
    }

    const conversation = this.conversationCache.get(sessionId);
    conversation.history.push({
      query,
      response,
      timestamp: Date.now(),
      ...metadata
    });

    // Keep only last 3 conversations to prevent memory bloat (as requested by user)
    if (conversation.history.length > 3) {
      conversation.history = conversation.history.slice(-3);
    }

    console.log(`💬 Added conversation entry to session: ${sessionId} (total: ${conversation.history.length})`);
  }

  /**
   * Get conversation history for session
   * @param {string} sessionId - Session ID
   * @returns {Array} Conversation history or empty array
   */
  getConversationHistory(sessionId) {
    if (!this.conversationCache.has(sessionId)) {
      return [];
    }

    const conversation = this.conversationCache.get(sessionId);

    if (Date.now() > conversation.expiresAt) {
      this.conversationCache.delete(sessionId);
      console.log(`⏰ Conversation history expired for session: ${sessionId}`);
      return [];
    }

    return conversation.history;
  }

  /**
   * Get formatted conversation history for LLM consumption
   * @param {string} sessionId - Session ID
   * @returns {Array} Formatted conversation history for OpenRouter
   */
  getFormattedConversationHistory(sessionId) {
    const history = this.getConversationHistory(sessionId);

    if (history.length === 0) {
      console.log(`💬 No conversation history found for session: ${sessionId} (this is normal for first request)`);
      return [];
    }

    // Convert conversation history to OpenRouter format
    const formattedHistory = [];

    history.forEach((entry, index) => {
      // Add user message
      formattedHistory.push({
        role: 'user',
        content: entry.query
      });

      // Add assistant response
      formattedHistory.push({
        role: 'assistant',
        content: entry.response
      });

      console.log(`💬 [${index + 1}] Previous conversation: "${entry.query.substring(0, 50)}..." → "${entry.response.substring(0, 50)}..."`);
    });

    console.log(`💬 Retrieved ${history.length} conversation entries (${formattedHistory.length} messages) for session: ${sessionId}`);
    return formattedHistory;
  }

  /**
   * Get or create session for an appId
   * @param {string} appId - Application ID
   * @param {string} sessionId - Optional session ID
   * @returns {string} Session ID
   */
  getOrCreateSession(appId, sessionId = null) {
    if (sessionId && this.sessionCache.has(sessionId)) {
      const session = this.sessionCache.get(sessionId);
      if (session.appId === appId && !this.isExpired(session)) {
        console.log(`📋 Using existing session: ${sessionId}`);
        return sessionId;
      }
    }

    // Create new session
    const newSessionId = this.generateSessionId();
    console.log(`🆕 Creating new session: ${newSessionId} for appId: ${appId}`);
    return newSessionId;
  }

  /**
   * Cache documents for a session
   * @param {string} sessionId - Session ID
   * @param {string} appId - Application ID
   * @param {Array} documents - Documents array
   * @param {string} authToken - Auth token for future validation
   */
  cacheDocuments(sessionId, appId, documents, authToken) {
    // Check cache size limit
    if (this.sessionCache.size >= this.maxSessions) {
      this.cleanupExpiredSessions();

      // If still at limit, remove oldest session
      if (this.sessionCache.size >= this.maxSessions) {
        const oldestSession = this.getOldestSession();
        if (oldestSession) {
          this.sessionCache.delete(oldestSession);
          console.log(`🗑️ Removed oldest session due to cache limit: ${oldestSession}`);
        }
      }
    }

    const sessionData = {
      sessionId,
      appId,
      documents,
      authToken,
      timestamp: Date.now(),
      lastAccessed: Date.now(),
      accessCount: 1
    };

    this.sessionCache.set(sessionId, sessionData);

    // Also cache by appId for quick lookup
    this.appCache.set(appId, {
      sessionId,
      timestamp: Date.now()
    });

    console.log(`💾 Cached ${documents.length} documents for session: ${sessionId}`);
  }

  /**
   * Get cached documents for a session
   * @param {string} sessionId - Session ID
   * @returns {Object|null} Session data or null if not found/expired
   */
  getCachedDocuments(sessionId) {
    if (!this.sessionCache.has(sessionId)) {
      return null;
    }

    const session = this.sessionCache.get(sessionId);

    if (this.isExpired(session)) {
      this.sessionCache.delete(sessionId);
      this.appCache.delete(session.appId);
      console.log(`⏰ Session expired and removed: ${sessionId}`);
      return null;
    }

    // Update last accessed time
    session.lastAccessed = Date.now();
    session.accessCount++;

    console.log(`📋 Retrieved cached documents for session: ${sessionId} (access count: ${session.accessCount})`);
    return session;
  }

  /**
   * Get session by appId
   * @param {string} appId - Application ID
   * @returns {string|null} Session ID or null if not found
   */
  getSessionByAppId(appId) {
    if (!this.appCache.has(appId)) {
      return null;
    }

    const appData = this.appCache.get(appId);
    const session = this.sessionCache.get(appData.sessionId);

    if (!session || this.isExpired(session)) {
      this.appCache.delete(appId);
      if (session) {
        this.sessionCache.delete(appData.sessionId);
      }
      return null;
    }

    return appData.sessionId;
  }

  /**
   * Invalidate session
   * @param {string} sessionId - Session ID
   */
  invalidateSession(sessionId) {
    if (this.sessionCache.has(sessionId)) {
      const session = this.sessionCache.get(sessionId);
      this.sessionCache.delete(sessionId);
      this.appCache.delete(session.appId);
      console.log(`🗑️ Invalidated session: ${sessionId}`);
    }
  }

  /**
   * Invalidate all sessions for an appId
   * @param {string} appId - Application ID
   */
  invalidateAppSessions(appId) {
    const sessionId = this.getSessionByAppId(appId);
    if (sessionId) {
      this.invalidateSession(sessionId);
    }
  }

  /**
   * Check if session is expired
   * @param {Object} session - Session object
   * @returns {boolean} True if expired
   */
  isExpired(session) {
    return Date.now() - session.timestamp > this.ttl;
  }

  /**
   * Get oldest session ID
   * @returns {string|null} Oldest session ID
   */
  getOldestSession() {
    let oldestSession = null;
    let oldestTime = Date.now();

    for (const [sessionId, session] of this.sessionCache.entries()) {
      if (session.timestamp < oldestTime) {
        oldestTime = session.timestamp;
        oldestSession = sessionId;
      }
    }

    return oldestSession;
  }

  /**
   * Cleanup expired sessions and caches
   */
  cleanupExpiredSessions() {
    let cleanedCount = 0;
    const now = Date.now();

    // Cleanup expired sessions
    for (const [sessionId, session] of this.sessionCache.entries()) {
      if (this.isExpired(session)) {
        this.sessionCache.delete(sessionId);
        this.appCache.delete(session.appId);
        cleanedCount++;
      }
    }

    // Cleanup expired API key validations
    let apiKeyCleanedCount = 0;
    for (const [hashedKey, cached] of this.apiKeyCache.entries()) {
      if (now > cached.expiresAt) {
        this.apiKeyCache.delete(hashedKey);
        apiKeyCleanedCount++;
      }
    }

    // Cleanup expired contexts
    let contextCleanedCount = 0;
    for (const [cacheKey, cached] of this.contextCache.entries()) {
      if (now > cached.expiresAt) {
        this.contextCache.delete(cacheKey);
        contextCleanedCount++;
      }
    }

    // Cleanup expired documents
    let documentCleanedCount = 0;
    for (const [cacheKey, cached] of this.documentCache.entries()) {
      if (now > cached.expiresAt) {
        this.documentCache.delete(cacheKey);
        documentCleanedCount++;
      }
    }

    // Cleanup expired conversations
    let conversationCleanedCount = 0;
    for (const [sessionId, conversation] of this.conversationCache.entries()) {
      if (now > conversation.expiresAt) {
        this.conversationCache.delete(sessionId);
        conversationCleanedCount++;
      }
    }

    if (cleanedCount > 0 || apiKeyCleanedCount > 0 || contextCleanedCount > 0 || documentCleanedCount > 0 || conversationCleanedCount > 0) {
      console.log(`🧹 Cleanup completed - Sessions: ${cleanedCount}, API Keys: ${apiKeyCleanedCount}, Contexts: ${contextCleanedCount}, Documents: ${documentCleanedCount}, Conversations: ${conversationCleanedCount}`);
    }
  }

  /**
   * Start automatic cleanup interval
   */
  startCleanupInterval() {
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, this.cleanupInterval);

    console.log(`🔄 Started cache cleanup interval: ${config.cache.cleanupIntervalMinutes} minutes`);
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache stats
   */
  getStats() {
    return {
      sessions: {
        total: this.sessionCache.size,
        maxSessions: this.maxSessions,
        ttlMinutes: config.cache.ttlMinutes
      },
      apps: {
        total: this.appCache.size
      },
      apiKeys: {
        total: this.apiKeyCache.size,
        ttlMinutes: this.apiKeyTTL / (60 * 1000)
      },
      contexts: {
        total: this.contextCache.size,
        ttlMinutes: this.contextTTL / (60 * 1000)
      },
      documents: {
        total: this.documentCache.size,
        ttlMinutes: this.documentTTL / (60 * 1000)
      },
      conversations: {
        total: this.conversationCache.size,
        ttlMinutes: this.conversationTTL / (60 * 1000)
      },
      cleanup: {
        intervalMinutes: config.cache.cleanupIntervalMinutes
      }
    };
  }

  /**
   * Clear all cache
   */
  clearAll() {
    this.sessionCache.clear();
    this.appCache.clear();
    this.apiKeyCache.clear();
    this.contextCache.clear();
    this.documentCache.clear();
    this.conversationCache.clear();
    console.log('🗑️ Cleared all caches (sessions, apps, apiKeys, contexts, documents, conversations)');
  }

  /**
   * Invalidate API key cache
   * @param {string} apiKey - API key to invalidate
   */
  invalidateApiKey(apiKey) {
    const hashedKey = this.hashApiKey(apiKey);
    if (this.apiKeyCache.has(hashedKey)) {
      this.apiKeyCache.delete(hashedKey);
      console.log(`🗑️ Invalidated API key cache: ${hashedKey.substring(0, 8)}...`);
    }
  }

  /**
   * Invalidate context cache for appId
   * @param {string} appId - Application ID
   */
  invalidateContextForApp(appId) {
    let deletedCount = 0;
    for (const [cacheKey] of this.contextCache.entries()) {
      if (cacheKey.startsWith(appId + ':')) {
        this.contextCache.delete(cacheKey);
        deletedCount++;
      }
    }
    if (deletedCount > 0) {
      console.log(`🗑️ Invalidated ${deletedCount} context entries for appId: ${appId}`);
    }
  }
}

module.exports = new CacheService();
