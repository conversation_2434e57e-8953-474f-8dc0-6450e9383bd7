/**
 * Integration test for conversation memory in the API endpoint
 * Tests that conversation history is properly passed to OpenRouter
 */

const fetch = require('node-fetch');

// Test configuration
const BASE_URL = 'http://localhost:3002';
const TEST_API_KEY = 'test_api_key_1751884336144_vp9gospvg';

async function testConversationIntegration() {
  console.log('🧪 Testing Conversation Memory Integration');
  console.log('==========================================\n');

  let sessionId = null;

  try {
    // Test 1: First query (should create new session)
    console.log('Test 1: First query - "What is ChatAI?"');
    const response1 = await fetch(`${BASE_URL}/api/v1/?apikey=${TEST_API_KEY}&query=What is ChatAI?&testMode=true&stream=false`);
    const data1 = await response1.json();
    
    if (data1.error) {
      throw new Error(`API Error: ${data1.message || 'Unknown error'}`);
    }
    
    sessionId = data1.sessionId;
    console.log(`✅ Session created: ${sessionId}`);
    console.log(`✅ Response received: "${data1.response.substring(0, 100)}..."`);
    console.log('');

    // Wait a moment to ensure the conversation is stored
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test 2: Follow-up query with same session (should include conversation history)
    console.log('Test 2: Follow-up query - "How does it work?" (with conversation history)');
    const response2 = await fetch(`${BASE_URL}/api/v1/?apikey=${TEST_API_KEY}&query=How does it work?&sessionId=${sessionId}&testMode=true&stream=false`);
    const data2 = await response2.json();
    
    if (data2.error) {
      throw new Error(`API Error: ${data2.message || 'Unknown error'}`);
    }
    
    console.log(`✅ Same session used: ${data2.sessionId === sessionId}`);
    console.log(`✅ Response received: "${data2.response.substring(0, 100)}..."`);
    console.log('');

    // Test 3: Third query (should maintain conversation context)
    console.log('Test 3: Third query - "What are the benefits?" (with conversation history)');
    const response3 = await fetch(`${BASE_URL}/api/v1/?apikey=${TEST_API_KEY}&query=What are the benefits?&sessionId=${sessionId}&testMode=true&stream=false`);
    const data3 = await response3.json();
    
    if (data3.error) {
      throw new Error(`API Error: ${data3.message || 'Unknown error'}`);
    }
    
    console.log(`✅ Same session used: ${data3.sessionId === sessionId}`);
    console.log(`✅ Response received: "${data3.response.substring(0, 100)}..."`);
    console.log('');

    // Test 4: Fourth query (should limit to last 3 conversations)
    console.log('Test 4: Fourth query - "Can I integrate it?" (should limit history)');
    const response4 = await fetch(`${BASE_URL}/api/v1/?apikey=${TEST_API_KEY}&query=Can I integrate it?&sessionId=${sessionId}&testMode=true&stream=false`);
    const data4 = await response4.json();
    
    if (data4.error) {
      throw new Error(`API Error: ${data4.message || 'Unknown error'}`);
    }
    
    console.log(`✅ Same session used: ${data4.sessionId === sessionId}`);
    console.log(`✅ Response received: "${data4.response.substring(0, 100)}..."`);
    console.log('');

    // Test 5: New session (should start fresh)
    console.log('Test 5: New session - "Hello" (should start fresh)');
    const response5 = await fetch(`${BASE_URL}/api/v1/?apikey=${TEST_API_KEY}&query=Hello&testMode=true&stream=false`);
    const data5 = await response5.json();
    
    if (data5.error) {
      throw new Error(`API Error: ${data5.message || 'Unknown error'}`);
    }
    
    const newSessionId = data5.sessionId;
    console.log(`✅ New session created: ${newSessionId}`);
    console.log(`✅ Different from previous: ${newSessionId !== sessionId}`);
    console.log(`✅ Response received: "${data5.response.substring(0, 100)}..."`);
    console.log('');

    console.log('🎉 All integration tests completed successfully!');
    console.log('==========================================');
    
    return {
      success: true,
      originalSessionId: sessionId,
      newSessionId: newSessionId,
      sessionsAreDifferent: newSessionId !== sessionId
    };

  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Helper function to test streaming endpoint
async function testStreamingConversation() {
  console.log('\n🧪 Testing Streaming Conversation Memory');
  console.log('=========================================\n');

  try {
    console.log('Test: Streaming with conversation history');
    const response = await fetch(`${BASE_URL}/api/v1/?apikey=${TEST_API_KEY}&query=What is vector search?&testMode=true&stream=true`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    let sessionId = null;
    let fullResponse = '';
    
    // Read the streaming response
    const reader = response.body;
    reader.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            if (data.type === 'session') {
              sessionId = data.sessionId;
              console.log(`✅ Streaming session created: ${sessionId}`);
            } else if (data.type === 'content') {
              fullResponse += data.content;
            } else if (data.type === 'done') {
              console.log(`✅ Streaming completed`);
              console.log(`✅ Full response: "${fullResponse.substring(0, 100)}..."`);
            }
          } catch (e) {
            // Ignore JSON parse errors for non-JSON lines
          }
        }
      }
    });

    return new Promise((resolve) => {
      reader.on('end', () => {
        resolve({
          success: true,
          sessionId: sessionId,
          responseLength: fullResponse.length
        });
      });
    });

  } catch (error) {
    console.error('❌ Streaming test failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Run the tests
if (require.main === module) {
  (async () => {
    try {
      console.log('🚀 Starting conversation memory integration tests...\n');
      
      const result1 = await testConversationIntegration();
      console.log('\n📊 Integration Test Results:', result1);
      
      if (result1.success) {
        console.log('\n✅ All tests passed! Conversation memory is working correctly.');
      } else {
        console.log('\n❌ Tests failed. Please check the server and try again.');
        process.exit(1);
      }
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    }
  })();
}

module.exports = { testConversationIntegration, testStreamingConversation };
