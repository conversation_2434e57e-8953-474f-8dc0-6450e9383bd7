/**
 * Test script for conversation memory functionality
 * Tests the session-based memory system that keeps last 3 prompts and responses
 */

const cacheService = require('./src/services/cacheService');

function testConversationMemory() {
  console.log('🧪 Testing Conversation Memory System');
  console.log('=====================================\n');

  const testSessionId = 'test-session-123';

  // Test 1: Empty conversation history
  console.log('Test 1: Empty conversation history');
  let history = cacheService.getFormattedConversationHistory(testSessionId);
  console.log(`✅ Empty history length: ${history.length} (expected: 0)`);
  console.log('');

  // Test 2: Add first conversation
  console.log('Test 2: Add first conversation');
  cacheService.addConversationEntry(testSessionId, 'What is ChatAI?', 'ChatAI is an AI-powered chat system.');
  history = cacheService.getFormattedConversationHistory(testSessionId);
  console.log(`✅ After 1 conversation - history length: ${history.length} (expected: 2)`);
  console.log('History:', JSON.stringify(history, null, 2));
  console.log('');

  // Test 3: Add second conversation
  console.log('Test 3: Add second conversation');
  cacheService.addConversationEntry(testSessionId, 'How does it work?', 'It uses vector search and LLM to provide contextual responses.');
  history = cacheService.getFormattedConversationHistory(testSessionId);
  console.log(`✅ After 2 conversations - history length: ${history.length} (expected: 4)`);
  console.log('');

  // Test 4: Add third conversation
  console.log('Test 4: Add third conversation');
  cacheService.addConversationEntry(testSessionId, 'What are the benefits?', 'Benefits include accurate responses, context awareness, and scalability.');
  history = cacheService.getFormattedConversationHistory(testSessionId);
  console.log(`✅ After 3 conversations - history length: ${history.length} (expected: 6)`);
  console.log('');

  // Test 5: Add fourth conversation (should remove the first one)
  console.log('Test 5: Add fourth conversation (should limit to 3)');
  cacheService.addConversationEntry(testSessionId, 'Can I integrate it?', 'Yes, you can integrate it using our API.');
  history = cacheService.getFormattedConversationHistory(testSessionId);
  console.log(`✅ After 4 conversations - history length: ${history.length} (expected: 6, limited to last 3 conversations)`);

  // Check that the first conversation was removed
  const firstQuery = history.find(msg => msg.content === 'What is ChatAI?');
  console.log(`✅ First conversation removed: ${!firstQuery} (expected: true)`);

  // Check that the latest conversation is present
  const latestQuery = history.find(msg => msg.content === 'Can I integrate it?');
  console.log(`✅ Latest conversation present: ${!!latestQuery} (expected: true)`);
  console.log('');

  // Test 6: Verify conversation format
  console.log('Test 6: Verify conversation format');
  console.log('Final conversation history:');
  history.forEach((msg, index) => {
    console.log(`  ${index + 1}. Role: ${msg.role}, Content: "${msg.content.substring(0, 50)}..."`);
  });
  console.log('');

  // Test 7: Test with different session
  console.log('Test 7: Test with different session');
  const newSessionId = 'test-session-456';
  const newHistory = cacheService.getFormattedConversationHistory(newSessionId);
  console.log(`✅ New session history length: ${newHistory.length} (expected: 0)`);
  console.log('');

  console.log('🎉 All conversation memory tests completed!');
  console.log('=====================================');

  return {
    success: true,
    finalHistoryLength: history.length,
    sessionsIsolated: newHistory.length === 0
  };
}

// Run the test
if (require.main === module) {
  try {
    const result = testConversationMemory();
    console.log('\n📊 Test Results:', result);
    process.exit(0);
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

module.exports = { testConversationMemory };
