# Implementation Guide: Short-term Memory Buffer for RAG System

## 🎯 **Objective**
Implement persistent conversation history and short-term memory buffer to enable context-aware multi-turn conversations in the RAG system.

## 📋 **Current Problem**
- No conversation history storage
- Each query treated independently 
- No context continuity between messages
- Poor multi-turn conversation experience

## 🏗️ **Solution Architecture**

### **Database Layer (User-Service)**
- Add `ChatAiConversation` entity for conversation threading
- Enhance `ChatAiMessage` entity with conversation fields
- Create conversation management APIs

### **Processing Layer (ChatAI-SDK-Clean)**  
- Create memory buffer service
- Integrate conversation history with vector search
- Store responses with conversation context

## 📝 **Implementation Steps**

### **Step 1: Database Schema Changes**

#### **Create New Entity: `User-Service/src/chatAi/entities/conversation.entity.ts`**
```typescript
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  JoinC<PERSON>umn,
} from 'typeorm';
import { ChatAi } from './chatAi.entity';
import { ChatAiMessage } from './message.entity';

@Entity('chat_ai_conversations')
export class ChatAiConversation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  title: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'timestamp', nullable: true })
  lastMessageAt: Date;

  @Column({ type: 'integer', default: 0 })
  messageCount: number;

  @Column({ type: 'varchar', nullable: false })
  userId: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @ManyToOne(() => ChatAi, (chatAi) => chatAi.conversations, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'chatAiId' })
  chatAi: ChatAi;

  @Column({ nullable: false })
  chatAiId: string;

  @OneToMany(() => ChatAiMessage, (message) => message.conversation, {
    cascade: true,
  })
  messages: ChatAiMessage[];
}
```

#### **Update `User-Service/src/chatAi/entities/message.entity.ts`**
Add these imports and fields:
```typescript
import { ChatAiConversation } from './conversation.entity';

// Add these new columns to ChatAiMessage entity:
@Column({ type: 'integer', nullable: false })
messageOrder: number;

@Column({ type: 'text', nullable: true })
contextUsed: string;

@Column({ type: 'integer', nullable: true })
tokenCount: number;

@ManyToOne(() => ChatAiConversation, (conversation) => conversation.messages, {
  onDelete: 'CASCADE',
})
@JoinColumn({ name: 'conversationId' })
conversation: ChatAiConversation;

@Column({ type: 'uuid', nullable: true })
conversationId: string;
```

#### **Update `User-Service/src/chatAi/entities/chatAi.entity.ts`**
Add conversation relationship:
```typescript
import { ChatAiConversation } from './conversation.entity';

@OneToMany(() => ChatAiConversation, (conversation) => conversation.chatAi)
conversations: ChatAiConversation[];
```

### **Step 2: Create DTOs**

#### **Create `User-Service/src/chatAi/dto/conversation.dto.ts`**
```typescript
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsUUID, IsOptional, IsBoolean, IsNumber, Min, Max } from 'class-validator';

export class CreateConversationDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;

  @ApiPropertyOptional({ description: 'Conversation title' })
  @IsOptional()
  @IsString()
  title?: string;
}

export class GetMemoryBufferDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;

  @ApiProperty({ description: 'Conversation ID' })
  @IsUUID()
  conversationId: string;

  @ApiPropertyOptional({ description: 'Maximum messages to include', default: 10 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(20)
  maxMessages?: number = 10;

  @ApiPropertyOptional({ description: 'Maximum tokens for memory buffer', default: 2000 })
  @IsOptional()
  @IsNumber()
  @Min(100)
  @Max(4000)
  maxTokens?: number = 2000;
}

export class StoreConversationMessageDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;

  @ApiProperty({ description: 'Conversation ID' })
  @IsUUID()
  conversationId: string;

  @ApiProperty({ description: 'User question' })
  @IsString()
  question: string;

  @ApiProperty({ description: 'AI response' })
  @IsString()
  response: string;

  @ApiPropertyOptional({ description: 'Context used for response' })
  @IsOptional()
  @IsString()
  contextUsed?: string;

  @ApiPropertyOptional({ description: 'Source references' })
  @IsOptional()
  sourceReferences?: any;

  @ApiPropertyOptional({ description: 'Token count' })
  @IsOptional()
  @IsNumber()
  tokenCount?: number;
}
```

### **Step 3: Create Conversation Service**

#### **Create `User-Service/src/chatAi/services/conversation.service.ts`**
Key methods needed:
- `createConversation()` - Create new conversation
- `getMemoryBuffer()` - Get formatted conversation history for LLM
- `storeConversationMessage()` - Store message with conversation context
- `listConversations()` - List user conversations
- `deleteConversation()` - Delete conversation and messages

### **Step 4: Create API Controller**

#### **Create `User-Service/src/chatAi/controllers/conversation.controller.ts`**
Expose REST endpoints:
- `POST /users/app/chatai/conversations` - Create conversation
- `GET /users/app/chatai/conversations/:id/memory-buffer` - Get memory buffer
- `POST /users/app/chatai/conversations/:id/messages` - Store message
- `GET /users/app/chatai/conversations` - List conversations
- `DELETE /users/app/chatai/conversations/:id` - Delete conversation

### **Step 5: Database Migration**

#### **Create `User-Service/src/migrations/TIMESTAMP-AddConversationSupport.ts`**
```typescript
export class AddConversationSupport implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create chat_ai_conversations table
    await queryRunner.query(`
      CREATE TABLE "chat_ai_conversations" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "title" character varying(255),
        "isActive" boolean NOT NULL DEFAULT true,
        "lastMessageAt" TIMESTAMP,
        "messageCount" integer NOT NULL DEFAULT 0,
        "userId" character varying NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "chatAiId" uuid NOT NULL,
        CONSTRAINT "PK_chat_ai_conversations" PRIMARY KEY ("id")
      )
    `);

    // Add new columns to chat_ai_messages
    await queryRunner.query(`
      ALTER TABLE "chat_ai_messages" 
      ADD COLUMN "conversationId" uuid,
      ADD COLUMN "messageOrder" integer NOT NULL DEFAULT 1,
      ADD COLUMN "contextUsed" text,
      ADD COLUMN "tokenCount" integer
    `);

    // Add foreign key constraints and indexes
    // ... (add proper constraints and indexes)
  }
}
```

### **Step 6: Update Module Configuration**

#### **Update `User-Service/src/chatAi/chatAi.module.ts`**
```typescript
import { ConversationService } from './services/conversation.service';
import { ConversationController } from './controllers/conversation.controller';
import { ChatAiConversation } from './entities/conversation.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      // ... existing entities
      ChatAiConversation, // Add this
    ]),
  ],
  providers: [ChatAiService, ConversationService], // Add ConversationService
  controllers: [ChatAiController, ConversationController], // Add ConversationController
  exports: [ChatAiService, ConversationService], // Add ConversationService
})
```

### **Step 7: ChatAI-SDK-Clean Integration**

#### **Create `ChatAI-SDK-Clean/src/services/memoryBufferService.js`**
```javascript
class MemoryBufferService {
  constructor() {
    this.userServiceBaseUrl = process.env.USER_SERVICE_URL || 'http://localhost:3000';
  }

  async getConversationHistory(conversationId, maxMessages = 10, maxTokens = 2000) {
    // Call User-Service API to get memory buffer
    const response = await fetch(
      `${this.userServiceBaseUrl}/users/app/chatai/conversations/${conversationId}/memory-buffer?maxMessages=${maxMessages}&maxTokens=${maxTokens}`
    );
    
    if (!response.ok) return { history: [], tokenCount: 0 };
    
    const data = await response.json();
    return data.result;
  }

  formatContextWithHistory(vectorContext, conversationHistory) {
    // Combine vector search results with conversation history
    let context = `Document Context:\n${vectorContext}\n\n`;
    
    if (conversationHistory.length > 0) {
      context += `Conversation History:\n`;
      for (const msg of conversationHistory) {
        context += `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}\n`;
      }
      context += `\n`;
    }
    
    return context;
  }
}
```

#### **Update `ChatAI-SDK-Clean/src/routes/index.js`**
Modify main chat endpoint to:
1. Accept `conversationId` parameter
2. Get conversation history if conversationId provided
3. Combine conversation history with vector context
4. Store response back to User-Service

## 🚀 **Implementation Priority**

1. **Database Schema** (entities, migration)
2. **User-Service APIs** (service, controller, DTOs)
3. **Run Migration** 
4. **ChatAI-SDK-Clean Integration**
5. **End-to-end Testing**

## 🧠 **Memory Buffer Flow**

```
User Query + conversationId → ChatAI-SDK-Clean
                           ↓
Get Conversation History ← User-Service API
                           ↓
Vector Search → Qdrant → Combine with History
                           ↓
LLM Generation → OpenRouter (with full context)
                           ↓
Store Response → User-Service API
                           ↓
Response → User
```

## 🎯 **Expected Outcome**

- ✅ Persistent conversation history across sessions
- ✅ Context-aware responses that remember previous interactions  
- ✅ Conversation threading and management
- ✅ Token-managed memory buffer
- ✅ Clean API separation between services

This implementation will transform the RAG system from stateless queries to stateful conversations with persistent memory.
