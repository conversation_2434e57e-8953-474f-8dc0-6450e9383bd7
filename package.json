{"name": "user-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^3.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.3.3", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.3.4", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^7.3.0", "@nestjs/typeorm": "^10.0.2", "@openchainxyz/abi-guesser": "^1.0.2", "@types/form-data": "^2.2.1", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.3", "axios": "^1.6.7", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto-js": "^4.2.0", "decimal.js": "^10.5.0", "ethers": "^6.13.4", "express": "^5.1.0", "form-data": "^4.0.3", "helmet": "^7.1.0", "node-fetch": "^3.3.2", "nodemailer": "^6.9.9", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "pug": "^3.0.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "typeorm": "^0.3.20", "viem": "^2.21.51", "zod": "^3.22.4"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^2.0.0", "@types/node": "^20.3.1", "@types/socket.io-client": "^3.0.0", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}